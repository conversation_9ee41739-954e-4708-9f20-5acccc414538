from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Query, Depends
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Optional
import os

from ..models.person import PersonCreate, PersonResponse, PersonUpdate
from ..services.person_service import PersonService
from ..services.face_recognition_service import FaceRecognitionService
from ..utils.file_utils import save_upload_file, validate_file_type, validate_file_size

router = APIRouter(prefix="/api/persons", tags=["Quản lý người"])

# Dependencies
def get_person_service() -> PersonService:
    return PersonService()

def get_face_service() -> FaceRecognitionService:
    return FaceRecognitionService()

@router.post("/register", response_model=PersonResponse, summary="Đăng ký người mới")
async def register_person(
    name: str = Form(..., description="Tên người"),
    email: Optional[str] = Form(None, description="Email"),
    phone: Optional[str] = Form(None, description="Số điện thoại"),
    description: Optional[str] = Form(None, description="Mô tả"),
    image: UploadFile = File(..., description="Ảnh khuôn mặt"),
    person_service: PersonService = Depends(get_person_service),
    face_service: FaceRecognitionService = Depends(get_face_service)
):
    """Đăng ký người mới với ảnh khuôn mặt"""
    
    # Validate file type
    if not validate_file_type(image.filename):
        raise HTTPException(status_code=400, detail="Chỉ chấp nhận file ảnh (.jpg, .jpeg, .png)")
    
    # Validate file size
    if not validate_file_size(image.size):
        raise HTTPException(status_code=400, detail="Kích thước file không được vượt quá 10MB")
    
    try:
        # Save image
        image_path = await save_upload_file(image, "public/images")
        print(f"Image saved to {image_path}")
        
        # Validate image
        if not face_service.validate_image(image_path):
            os.remove(image_path)
            raise HTTPException(status_code=400, detail="File ảnh không hợp lệ")
        
        # Resize if needed
        processed_image_path = face_service.resize_image_if_needed(image_path)
        
        # Extract face encoding
        face_encoding = face_service.extract_face_encoding(processed_image_path)
        if not face_encoding:
            # os.remove(processed_image_path)
            # if processed_image_path != image_path:
            #     os.remove(image_path)
            raise HTTPException(status_code=400, detail="Không phát hiện được khuôn mặt trong ảnh")
        
        # Check if email already exists
        if email:
            existing_person = await person_service.get_person_by_email(email)
            if existing_person:
                os.remove(processed_image_path)
                if processed_image_path != image_path:
                    os.remove(image_path)
                raise HTTPException(status_code=400, detail="Email đã tồn tại trong hệ thống")
        
        # Create person
        person_data = PersonCreate(name=name, email=email, phone=phone, description=description)
        person = await person_service.create_person(person_data, processed_image_path, face_encoding)
        
        return person_service.to_person_response(person)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi khi đăng ký người: {str(e)}")

@router.get("/", response_model=List[PersonResponse], summary="Lấy danh sách người")
async def get_persons(
    skip: int = Query(0, ge=0, description="Số bản ghi bỏ qua"),
    limit: int = Query(50, ge=1, le=100, description="Số bản ghi tối đa"),
    search: Optional[str] = Query(None, description="Tìm kiếm theo tên hoặc email"),
    person_service: PersonService = Depends(get_person_service)
):
    """Lấy danh sách người với phân trang và tìm kiếm"""
    try:
        if search:
            persons = await person_service.search_persons(search, skip, limit)
        else:
            persons = await person_service.get_all_persons(skip, limit)
        
        return [person_service.to_person_response(person) for person in persons]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi khi lấy danh sách người: {str(e)}")

@router.get("/{person_id}", response_model=PersonResponse, summary="Lấy thông tin người theo ID")
async def get_person(
    person_id: str,
    person_service: PersonService = Depends(get_person_service)
):
    """Lấy thông tin chi tiết của một người"""
    person = await person_service.get_person_by_id(person_id)
    if not person:
        raise HTTPException(status_code=404, detail="Không tìm thấy người")
    
    return person_service.to_person_response(person)

@router.put("/{person_id}", response_model=PersonResponse, summary="Cập nhật thông tin người")
async def update_person(
    person_id: str,
    update_data: PersonUpdate,
    person_service: PersonService = Depends(get_person_service)
):
    """Cập nhật thông tin người (không bao gồm ảnh)"""
    
    # Check if person exists
    existing_person = await person_service.get_person_by_id(person_id)
    if not existing_person:
        raise HTTPException(status_code=404, detail="Không tìm thấy người")
    
    # Check email uniqueness if email is being updated
    if update_data.email and update_data.email != existing_person.email:
        person_with_email = await person_service.get_person_by_email(update_data.email)
        if person_with_email:
            raise HTTPException(status_code=400, detail="Email đã tồn tại trong hệ thống")
    
    try:
        updated_person = await person_service.update_person(person_id, update_data)
        if not updated_person:
            raise HTTPException(status_code=500, detail="Không thể cập nhật thông tin")
        
        return person_service.to_person_response(updated_person)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi khi cập nhật: {str(e)}")

@router.delete("/{person_id}", summary="Xóa người")
async def delete_person(
    person_id: str,
    person_service: PersonService = Depends(get_person_service)
):
    """Xóa người khỏi hệ thống"""
    
    # Check if person exists
    person = await person_service.get_person_by_id(person_id)
    if not person:
        raise HTTPException(status_code=404, detail="Không tìm thấy người")
    
    try:
        # Delete image file
        if os.path.exists(person.image_path):
            os.remove(person.image_path)
        
        # Delete from database
        deleted = await person_service.delete_person(person_id)
        if not deleted:
            raise HTTPException(status_code=500, detail="Không thể xóa người")
        
        return JSONResponse(content={"message": "Đã xóa người thành công"})
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi khi xóa: {str(e)}")

@router.get("/stats/summary", summary="Thống kê tổng quan")
async def get_stats(
    person_service: PersonService = Depends(get_person_service)
):
    """Lấy thống kê tổng quan về số lượng người trong hệ thống"""
    try:
        total_persons = await person_service.get_persons_count()
        
        return {
            "total_persons": total_persons,
            "message": f"Hiện có {total_persons} người trong hệ thống"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi khi lấy thống kê: {str(e)}")