from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Form
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Optional
import os

from ..models.person import FaceRecognitionResult, FaceMatch
from ..services.person_service import PersonService
from ..services.face_recognition_service import FaceRecognitionService
from ..utils.file_utils import save_upload_file, validate_file_type, validate_file_size

router = APIRouter(prefix="/api/face-recognition", tags=["Nhận diện khuôn mặt"])

# Dependencies
def get_person_service() -> PersonService:
    return PersonService()

def get_face_service() -> FaceRecognitionService:
    return FaceRecognitionService()

@router.post("/verify", response_model=FaceRecognitionResult, summary="<PERSON>ác thực khuôn mặt")
async def verify_faces(
    image: UploadFile = File(..., description="Ảnh cần xác thực"),
    threshold: float = Form(0.5, description="Ngưỡng nhận diện (0.0-1.0)"),
    save_result_image: bool = Form(True, description="<PERSON><PERSON> lưu ảnh kết quả không"),
    person_service: PersonService = Depends(get_person_service),
    face_service: FaceRecognitionService = Depends(get_face_service)
):
    """Xác thực tất cả khuôn mặt trong ảnh với database"""

    # Validate input
    if threshold < 0.0 or threshold > 1.0:
        raise HTTPException(status_code=400, detail="Ngưỡng phải từ 0.0 đến 1.0")

    # Validate file type
    if not validate_file_type(image.filename):
        raise HTTPException(status_code=400, detail="Chỉ chấp nhận file ảnh (.jpg, .jpeg, .png)")

    # Validate file size
    if not validate_file_size(image.size):
        raise HTTPException(status_code=400, detail="Kích thước file không được vượt quá 10MB")

    try:
        # Save uploaded image
        image_path = await save_upload_file(image, "public/images")

        # Validate image
        if not face_service.validate_image(image_path):
            os.remove(image_path)
            raise HTTPException(status_code=400, detail="File ảnh không hợp lệ")

        # Resize if needed
        processed_image_path = face_service.resize_image_if_needed(image_path)

        # Detect faces in uploaded image
        face_encodings, face_locations = face_service.detect_faces_in_image(processed_image_path)

        if not face_encodings:
            # Clean up files
            os.remove(processed_image_path)
            if processed_image_path != image_path:
                os.remove(image_path)
            raise HTTPException(status_code=400, detail="Không phát hiện được khuôn mặt trong ảnh")

        # Get all known face encodings from database
        known_persons = await person_service.get_all_face_encodings()

        if not known_persons:
            # Clean up files
            os.remove(processed_image_path)
            if processed_image_path != image_path:
                os.remove(image_path)
            raise HTTPException(status_code=404, detail="Chưa có dữ liệu khuôn mặt trong hệ thống")

        # Prepare known encodings
        known_encodings = [person["face_encoding"] for person in known_persons]

        # Process each detected face
        matches = []
        matched_names = []
        face_service.tolerance = threshold  # Update tolerance

        for i, face_encoding in enumerate(face_encodings):
            # Compare with known faces
            face_matches, face_distances = face_service.compare_faces(known_encodings, face_encoding.tolist())

            # Find best match
            best_match_index = None
            if any(face_matches):
                # Get the best match (smallest distance among matches)
                matched_distances = [(idx, dist) for idx, (match, dist) in enumerate(zip(face_matches, face_distances)) if match]
                if matched_distances:
                    best_match_index, best_distance = min(matched_distances, key=lambda x: x[1])

                    # Create match result
                    matched_person = known_persons[best_match_index]
                    confidence = max(0.0, 1.0 - best_distance)  # Convert distance to confidence

                    face_match = FaceMatch(
                        person_id=matched_person["id"],
                        name=matched_person["name"],
                        email=matched_person.get("email"),
                        phone=matched_person.get("phone"),
                        description=matched_person.get("description"),
                        confidence=round(confidence, 3),
                        distance=round(best_distance, 3)
                    )
                    matches.append(face_match)
                    matched_names.append(matched_person["name"])
                else:
                    matched_names.append("")
            else:
                matched_names.append("")

        # Save result image with boxes if requested
        result_image_path = None
        if save_result_image:
            result_image_path = face_service.save_image_with_boxes(
                processed_image_path,
                face_locations,
                matched_names,
                "public/images"
            )

        # Clean up temporary files
        os.remove(processed_image_path)
        if processed_image_path != image_path:
            os.remove(image_path)

        return FaceRecognitionResult(
            total_faces_detected=len(face_encodings),
            matches=matches,
            image_path=result_image_path
        )

    except HTTPException:
        raise
    except Exception as e:
        # Clean up files on error
        if 'image_path' in locals() and os.path.exists(image_path):
            os.remove(image_path)
        if 'processed_image_path' in locals() and processed_image_path != image_path and os.path.exists(processed_image_path):
            os.remove(processed_image_path)
        raise HTTPException(status_code=500, detail=f"Lỗi khi xác thực khuôn mặt: {str(e)}")

@router.post("/verify-single", summary="Xác thực một khuôn mặt")
async def verify_single_face(
    image: UploadFile = File(..., description="Ảnh chứa một khuôn mặt"),
    threshold: float = Form(0.5, description="Ngưỡng nhận diện (0.0-1.0)"),
    person_service: PersonService = Depends(get_person_service),
    face_service: FaceRecognitionService = Depends(get_face_service)
):
    """Xác thực một khuôn mặt trong ảnh (tối ưu cho ảnh có 1 người)"""

    # Validate input
    if threshold < 0.0 or threshold > 1.0:
        raise HTTPException(status_code=400, detail="Ngưỡng phải từ 0.0 đến 1.0")

    # Validate file type
    if not validate_file_type(image.filename):
        raise HTTPException(status_code=400, detail="Chỉ chấp nhận file ảnh (.jpg, .jpeg, .png)")

    # Validate file size
    if not validate_file_size(image.size):
        raise HTTPException(status_code=400, detail="Kích thước file không được vượt quá 10MB")

    try:
        # Save uploaded image
        image_path = await save_upload_file(image, "public/images")

        # Validate image
        if not face_service.validate_image(image_path):
            os.remove(image_path)
            raise HTTPException(status_code=400, detail="File ảnh không hợp lệ")

        # Resize if needed
        processed_image_path = face_service.resize_image_if_needed(image_path)

        # Extract face encoding
        face_encoding = face_service.extract_face_encoding(processed_image_path)

        if not face_encoding:
            # Clean up files
            os.remove(processed_image_path)
            if processed_image_path != image_path:
                os.remove(image_path)
            raise HTTPException(status_code=400, detail="Không phát hiện được khuôn mặt trong ảnh")

        # Get all known face encodings from database
        known_persons = await person_service.get_all_face_encodings()

        if not known_persons:
            # Clean up files
            os.remove(processed_image_path)
            if processed_image_path != image_path:
                os.remove(image_path)
            raise HTTPException(status_code=404, detail="Chưa có dữ liệu khuôn mặt trong hệ thống")

        # Prepare known encodings
        known_encodings = [person["face_encoding"] for person in known_persons]

        # Compare faces
        face_service.tolerance = threshold
        face_matches, face_distances = face_service.compare_faces(known_encodings, face_encoding)

        # Clean up files
        os.remove(processed_image_path)
        if processed_image_path != image_path:
            os.remove(image_path)

        # Find best match
        if any(face_matches):
            # Get the best match (smallest distance among matches)
            matched_distances = [(idx, dist) for idx, (match, dist) in enumerate(zip(face_matches, face_distances)) if match]
            if matched_distances:
                best_match_index, best_distance = min(matched_distances, key=lambda x: x[1])

                # Get matched person info
                matched_person = known_persons[best_match_index]
                confidence = max(0.0, 1.0 - best_distance)

                return {
                    "match_found": True,
                    "person": {
                        "id": matched_person["id"],
                        "name": matched_person["name"],
                        "email": matched_person.get("email"),
                        "phone": matched_person.get("phone"),
                        "description": matched_person.get("description")
                    },
                    "confidence": round(confidence, 3),
                    "distance": round(best_distance, 3)
                }

        return {
            "match_found": False,
            "message": "Không tìm thấy khuôn mặt khớp trong hệ thống"
        }

    except HTTPException:
        raise
    except Exception as e:
        # Clean up files on error
        if 'image_path' in locals() and os.path.exists(image_path):
            os.remove(image_path)
        if 'processed_image_path' in locals() and processed_image_path != image_path and os.path.exists(processed_image_path):
            os.remove(processed_image_path)
        raise HTTPException(status_code=500, detail=f"Lỗi khi xác thực khuôn mặt: {str(e)}")

@router.get("/settings", summary="Lấy cài đặt nhận diện")
async def get_recognition_settings():
    """Lấy các cài đặt hiện tại của hệ thống nhận diện"""
    return {
        "default_tolerance": 0.5,
        "supported_formats": [".jpg", ".jpeg", ".png"],
        "max_file_size_mb": 10,
        "face_detection_model": "hog",
        "description": {
            "tolerance": "Ngưỡng nhận diện: thấp hơn = nghiêm ngặt hơn",
            "model": "hog = nhanh hơn, cnn = chính xác hơn nhưng chậm"
        }
    }
