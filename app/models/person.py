from pydantic import BaseModel, <PERSON>, ConfigDict
from typing import Optional, List, Any
from datetime import datetime
from bson import ObjectId


class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_json_schema__(cls, _source_type: Any, _handler) -> dict:
        return {"type": "string"}

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v, _info=None):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)


class PersonBase(BaseModel):
    name: str = Field(..., description="Tên của người")
    email: Optional[str] = Field(None, description="Email của người")
    phone: Optional[str] = Field(None, description="Số điện thoại")
    description: Optional[str] = Field(None, description="Mô tả thêm")


class PersonCreate(PersonBase):
    pass


class PersonUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    description: Optional[str] = None


class Person(PersonBase):
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str}
    )
    
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    image_path: str = Field(..., description="Đường dẫn ảnh")
    face_encoding: List[float] = Field(..., description="Mã hóa khuôn mặt")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class PersonResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: str
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    description: Optional[str] = None
    image_path: str
    created_at: datetime


class FaceMatch(BaseModel):
    person_id: str
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    description: Optional[str] = None
    confidence: float = Field(..., description="Độ tin cậy (0-1)")
    distance: float = Field(..., description="Khoảng cách Euclidean")


class FaceRecognitionResult(BaseModel):
    total_faces_detected: int = Field(..., description="Số khuôn mặt phát hiện")
    matches: List[FaceMatch] = Field(..., description="Danh sách kết quả khớp")
    image_path: Optional[str] = Field(None, description="Đường dẫn ảnh kết quả")