from typing import List, Optional
from bson import ObjectId
from pymongo.errors import Duplicate<PERSON>eyError, ServerSelectionTimeoutError
from datetime import datetime

from ..models.person import Person, PersonCreate, PersonUpdate, PersonResponse
from ..utils.database import get_collection, is_connected


class PersonService:
    def __init__(self):
        try:
            self.collection = get_collection("persons")
        except ConnectionError as e:
            print(f"Lỗi kết nối database: {e}")
            raise e
    
    async def create_person(self, person_data: PersonCreate, image_path: str, face_encoding: List[float]) -> Person:
        """Tạo người mới với thông tin và face encoding"""
        if not is_connected():
            raise ConnectionError("Database không được kết nối")
            
        person_dict = person_data.dict()
        person_dict.update({
            "image_path": image_path,
            "face_encoding": face_encoding,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        })
        
        try:
            result = await self.collection.insert_one(person_dict)
            person_dict["_id"] = result.inserted_id
            return Person(**person_dict)
        except DuplicateKeyError:
            raise ValueError("<PERSON><PERSON> đ<PERSON> tồn tại trong hệ thống")
        except ServerSelectionTimeoutError:
            raise ConnectionError("Không thể kết nối đến MongoDB")
        except Exception as e:
            raise RuntimeError(f"Lỗi khi tạo người: {str(e)}")
    
    async def get_person_by_id(self, person_id: str) -> Optional[Person]:
        """Lấy thông tin người theo ID"""
        if not is_connected():
            raise ConnectionError("Database không được kết nối")
            
        if not ObjectId.is_valid(person_id):
            return None
        
        try:
            person_doc = await self.collection.find_one({"_id": ObjectId(person_id)})
            if person_doc:
                return Person(**person_doc)
            return None
        except ServerSelectionTimeoutError:
            raise ConnectionError("Không thể kết nối đến MongoDB")
        except Exception as e:
            raise RuntimeError(f"Lỗi khi lấy thông tin người: {str(e)}")
    
    async def get_person_by_email(self, email: str) -> Optional[Person]:
        """Lấy thông tin người theo email"""
        person_doc = await self.collection.find_one({"email": email})
        if person_doc:
            return Person(**person_doc)
        return None
    
    async def get_all_persons(self, skip: int = 0, limit: int = 100) -> List[Person]:
        """Lấy danh sách tất cả người"""
        cursor = self.collection.find({}).skip(skip).limit(limit)
        persons = []
        async for person_doc in cursor:
            persons.append(Person(**person_doc))
        return persons
    
    async def get_all_face_encodings(self) -> List[dict]:
        """Lấy tất cả face encodings để so sánh"""
        if not is_connected():
            raise ConnectionError("Database không được kết nối")
            
        try:
            cursor = self.collection.find({}, {"_id": 1, "name": 1, "email": 1, "phone": 1, "description": 1, "face_encoding": 1})
            encodings = []
            async for doc in cursor:
                encodings.append({
                    "id": str(doc["_id"]),
                    "name": doc["name"],
                    "email": doc.get("email"),
                    "phone": doc.get("phone"),
                    "description": doc.get("description"),
                    "face_encoding": doc["face_encoding"]
                })
            return encodings
        except ServerSelectionTimeoutError:
            raise ConnectionError("Không thể kết nối đến MongoDB")
        except Exception as e:
            raise RuntimeError(f"Lỗi khi lấy face encodings: {str(e)}")
    
    async def update_person(self, person_id: str, update_data: PersonUpdate) -> Optional[Person]:
        """Cập nhật thông tin người"""
        if not ObjectId.is_valid(person_id):
            return None
        
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        if not update_dict:
            return await self.get_person_by_id(person_id)
        
        update_dict["updated_at"] = datetime.utcnow()
        
        result = await self.collection.update_one(
            {"_id": ObjectId(person_id)},
            {"$set": update_dict}
        )
        
        if result.modified_count:
            return await self.get_person_by_id(person_id)
        return None
    
    async def delete_person(self, person_id: str) -> bool:
        """Xóa người khỏi database"""
        if not ObjectId.is_valid(person_id):
            return False
        
        result = await self.collection.delete_one({"_id": ObjectId(person_id)})
        return result.deleted_count > 0
    
    async def search_persons(self, query: str, skip: int = 0, limit: int = 50) -> List[Person]:
        """Tìm kiếm người theo tên hoặc email"""
        search_filter = {
            "$or": [
                {"name": {"$regex": query, "$options": "i"}},
                {"email": {"$regex": query, "$options": "i"}}
            ]
        }
        
        cursor = self.collection.find(search_filter).skip(skip).limit(limit)
        persons = []
        async for person_doc in cursor:
            persons.append(Person(**person_doc))
        return persons
    
    async def get_persons_count(self) -> int:
        """Đếm tổng số người trong database"""
        return await self.collection.count_documents({})
    
    def to_person_response(self, person: Person) -> PersonResponse:
        """Chuyển đổi Person model thành PersonResponse"""
        return PersonResponse(
            id=str(person.id),
            name=person.name,
            email=person.email,
            phone=person.phone,
            description=person.description,
            image_path=person.image_path,
            created_at=person.created_at
        )