import face_recognition
import cv2
import numpy as np
from typing import List, Tuple, Optional
import os
from PIL import Image
import uuid
from datetime import datetime

class FaceRecognitionService:
    def __init__(self):
        self.tolerance = 0.5  # Ngưỡng nhận diện (thấp hơn = ng<PERSON><PERSON><PERSON> ngặt hơn)
        self.model = "hog"  # C<PERSON> thể dùng "cnn" cho độ chính xác cao hơn nhưng chậm hơn
    
    def extract_face_encoding(self, image_path: str) -> Optional[List[float]]:
        """Trích xuất mã hóa khuôn mặt từ ảnh"""
        try:
            # Load ảnh
            image = face_recognition.load_image_file(image_path)
            
            # Tìm vị trí khuôn mặt
            face_locations = face_recognition.face_locations(image, model=self.model)
            
            if not face_locations:
                return None
            
            # Lấy encoding của khuôn mặt đầu tiên
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            if face_encodings:
                return face_encodings[0].tolist()
            
            return None
            
        except Exception as e:
            print(f"Lỗi khi trích xuất face encoding: {e}")
            return None
    
    def detect_faces_in_image(self, image_path: str) -> Tuple[List[np.ndarray], List[Tuple[int, int, int, int]]]:
        """Phát hiện tất cả khuôn mặt trong ảnh"""
        try:
            # Load ảnh
            image = face_recognition.load_image_file(image_path)
            
            # Tìm vị trí khuôn mặt
            face_locations = face_recognition.face_locations(image, model=self.model)
            
            # Trích xuất encodings
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            return face_encodings, face_locations
            
        except Exception as e:
            print(f"Lỗi khi phát hiện khuôn mặt: {e}")
            return [], []
    
    def compare_faces(self, known_encodings: List[List[float]], face_encoding: List[float]) -> Tuple[List[bool], List[float]]:
        """So sánh khuôn mặt với database"""
        try:
            # Chuyển đổi về numpy array
            known_encodings_np = [np.array(encoding) for encoding in known_encodings]
            face_encoding_np = np.array(face_encoding)
            
            # So sánh
            matches = face_recognition.compare_faces(known_encodings_np, face_encoding_np, tolerance=self.tolerance)
            distances = face_recognition.face_distance(known_encodings_np, face_encoding_np)
            
            return matches, distances.tolist()
            
        except Exception as e:
            print(f"Lỗi khi so sánh khuôn mặt: {e}")
            return [], []
    
    def save_image_with_boxes(self, image_path: str, face_locations: List[Tuple[int, int, int, int]], 
                             matches: List[str], output_dir: str = "public/images") -> str:
        """Lưu ảnh với khung đánh dấu khuôn mặt"""
        try:
            # Load ảnh bằng OpenCV
            image = cv2.imread(image_path)
            
            # Vẽ khung cho mỗi khuôn mặt
            for i, (top, right, bottom, left) in enumerate(face_locations):
                # Chọn màu dựa trên kết quả nhận diện
                color = (0, 255, 0) if i < len(matches) and matches[i] else (0, 0, 255)
                
                # Vẽ khung
                cv2.rectangle(image, (left, top), (right, bottom), color, 2)
                
                # Thêm nhãn
                label = matches[i] if i < len(matches) and matches[i] else "Unknown"
                cv2.putText(image, label, (left, top - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # Tạo tên file unique
            filename = f"result_{uuid.uuid4().hex[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            output_path = os.path.join(output_dir, filename)
            
            # Tạo thư mục nếu chưa tồn tại
            os.makedirs(output_dir, exist_ok=True)
            
            # Lưu ảnh
            cv2.imwrite(output_path, image)
            
            return output_path
            
        except Exception as e:
            print(f"Lỗi khi lưu ảnh với khung: {e}")
            return ""
    
    def validate_image(self, image_path: str) -> bool:
        """Kiểm tra tính hợp lệ của ảnh"""
        try:
            with Image.open(image_path) as img:
                # Kiểm tra format
                if img.format not in ['JPEG', 'PNG', 'JPG']:
                    return False
                
                # Kiểm tra kích thước (không quá lớn)
                if img.width > 4000 or img.height > 4000:
                    return False
                
                return True
                
        except Exception:
            return False
    
    def resize_image_if_needed(self, image_path: str, max_width: int = 1920, max_height: int = 1080) -> str:
        """Resize ảnh nếu quá lớn"""
        try:
            with Image.open(image_path) as img:
                if img.width <= max_width and img.height <= max_height:
                    return image_path
                
                # Tính tỷ lệ resize
                ratio = min(max_width / img.width, max_height / img.height)
                new_size = (int(img.width * ratio), int(img.height * ratio))
                
                # Resize và lưu
                resized_img = img.resize(new_size, Image.Resampling.LANCZOS)
                resized_path = image_path.replace(".", "_resized.")
                resized_img.save(resized_path, quality=95)
                
                return resized_path
                
        except Exception as e:
            print(f"Lỗi khi resize ảnh: {e}")
            return image_path